<template>
  <el-dialog v-model="visible" title="选择数据集" width="80%" :before-close="handleClose" destroy-on-close>
    <div class="data-select-container">
      <!-- 搜索栏 -->
      <div class="search-bar mb-4">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入数据集名称搜索"
          clearable
          @input="onSearch"
          style="width: 300px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 数据集列表 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        class="c-table c-table-header"
        @selection-change="handleSelectionChange"
        :row-key="(row) => row.id"
        ref="tableRef"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="datasetName" label="量表英文名称" min-width="150" />
        <el-table-column prop="datasetNameCn" label="量表中文名称" min-width="150" />
        <el-table-column prop="projectCode" label="项目编号" width="120" />
        <el-table-column prop="diseaseType" label="病种" width="100" />
        <el-table-column prop="caseCount" label="病例数量" width="100" />
        <el-table-column prop="createDate" label="创建日期" width="120" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container mt-4 flex justify-center">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="saveLoading">
          确定选择 ({{ selectedDatasets.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { findFileInforByAnnotationId, newOrUpdateOrderItem_1 } from '@/api';
  import { ElMessage, ElTable } from 'element-plus';
  import { Search } from '@element-plus/icons-vue';
  import { debounce } from 'lodash-es';

  interface Props {
    visible: boolean;
    orderId: number;
    orderCode: string;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{
    'update:visible': [value: boolean];
    success: [];
  }>();

  // 响应式数据
  const loading = ref(false);
  const saveLoading = ref(false);
  const searchKeyword = ref('');
  const tableData = ref<FileInfoVO[]>([]);
  const selectedDatasets = ref<FileInfoVO[]>([]);
  const tableRef = ref<InstanceType<typeof ElTable>>();

  // 分页数据
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);

  // 计算属性
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  // 获取数据集列表
  const fetchDatasets = async (pageNum = 1) => {
    try {
      loading.value = true;
      const params: AnnotationIDListDTO = {
        annotationIDList: [],
        pageNum,
        pageSize: pagination.pageSize,
        otherFilter: searchKeyword.value,
      };

      const { data } = await findFileInforByAnnotationId(params);
      tableData.value = data?.content || [];
      total.value = data?.totalElement || 0;
    } catch (error) {
      console.error('获取数据集失败:', error);
      ElMessage.error('获取数据集失败');
    } finally {
      loading.value = false;
    }
  };

  // 处理选择变化
  const handleSelectionChange = (selection: FileInfoVO[]) => {
    // 直接更新选中的数据集列表
    selectedDatasets.value = selection;
  };

  // 搜索防抖
  const onSearch = debounce(() => {
    pagination.page = 1;
    fetchDatasets(1);
  }, 300);

  // 分页处理
  const handleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.page = 1;
    fetchDatasets(1);
  };

  const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchDatasets(page);
  };

  // 确认选择
  const handleConfirm = async () => {
    if (selectedDatasets.value.length === 0) {
      ElMessage.warning('请至少选择一个数据集');
      return;
    }

    try {
      saveLoading.value = true;

      // 构建订单项数据
      const params: NewOrderItemDTO[] = selectedDatasets.value.map((dataset) => ({
        fileInforId: dataset.id!,
        quantity: 1,
      }));

      // 调用API添加订单项
      await newOrUpdateOrderItem_1(params, { orderId: props.orderId });

      ElMessage.success(`成功添加 ${selectedDatasets.value.length} 个数据集到订单`);
      emit('success');
      handleClose();
    } catch (error) {
      console.error('添加数据集失败:', error);
      ElMessage.error('添加数据集失败');
    } finally {
      saveLoading.value = false;
    }
  };

  // 关闭对话框
  const handleClose = () => {
    // 重置状态
    selectedDatasets.value = [];
    searchKeyword.value = '';
    pagination.page = 1;
    // 清空表格选择状态
    tableRef.value?.clearSelection();
    visible.value = false;
  };

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (newVisible) => {
      if (newVisible) {
        fetchDatasets(1);
      }
    }
  );
</script>

<style lang="scss" scoped>
  .data-select-container {
    min-height: 400px;
  }

  .search-bar {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .pagination-container {
    margin-top: 16px;
  }

  .c-table {
    --el-border-color-lighter: #ebeef5;
  }

  :deep(.el-table .el-table__cell) {
    padding: 8px 0;
  }
</style>
