<template>
  <div class="bg-baf flex h-full flex-col">
    <div class="flex h-0 flex-1">
      <AsideBar @change="handleChange" />

      <el-scrollbar height="100%" class="bg-w w-0 flex-1 text-sm">
        <keep-alive>
          <component
            :is="currentComponent"
            :dataset-id="datasetId"
            :dataset-name="datasetName"
            :table-id="tableId"
            :table-name="tableName"
            :field-id="fieldId"
            :back-component="true"
            @goto-table="handleGotoTable"
            @goto-dataset="handleGotoDataset"
            @goto-filed="handleGotoFiled"
            @back-table="handleBackTable"
          />
        </keep-alive>
      </el-scrollbar>
    </div>

    <div class="box-shadow bg-w flex h-[68px] items-center justify-center">
      <el-button type="primary" :loading="saveLoading" @click="onSave"> 保存 </el-button>
      <el-button @click="onQuit"> 退出 </el-button>
      <span class="ml-7 text-sm"> 订单编号：{{ code }} </span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import AsideBar from './components/AsideBar.vue';
  import { useRouter } from 'vue-router';
  import { newOrUpdateOrderItem_1, submitOrder } from '@/api';
  import { ElMessage } from 'element-plus';
  import { useDataBrowse } from '@/store/data-browse';
  import DatasetList from './dataset-list.vue';
  import DataTableList from './datatable-list.vue';
  import FieldDetail from '@/views/data-resource/data-browse/field-detail.vue';

  interface Props {
    orderId?: string;
    code?: string;
  }
  const props = defineProps<Props>();
  const store = useDataBrowse();
  const router = useRouter();
  const saveLoading = ref(false);
  const currentComponentName = ref('DatasetList');
  const datasetId = ref(0);
  const datasetName = ref('');
  const tableId = ref(0);
  const tableName = ref('');
  const fieldId = ref(0);

  const currentComponent = computed(() => {
    switch (currentComponentName.value) {
      case 'DatasetList':
        return DatasetList;
      case 'DataTableList':
        return DataTableList;
      case 'FieldDetail':
        return FieldDetail;
      default:
        return DatasetList;
    }
  });

  const handleChange = (data: { id: number; name: string }) => {
    datasetId.value = data.id;
    datasetName.value = data.name;
    currentComponentName.value = 'DatasetList';
  };

  const handleGotoDataset = () => {
    currentComponentName.value = 'DatasetList';
  };

  const handleGotoTable = (id: number, name: string) => {
    tableId.value = id;
    tableName.value = name;
    currentComponentName.value = 'DataTableList';
  };

  const handleGotoFiled = (id: number) => {
    fieldId.value = id;
    currentComponentName.value = 'FieldDetail';
  };

  const handleBackTable = () => {
    currentComponentName.value = 'DataTableList';
  };

  const onSave = async () => {
    if (!store.dataBrowse.fileds.length) {
      ElMessage({ type: 'warning', message: '请先选择数据' });
      return;
    }

    try {
      saveLoading.value = true;
      const orderId = +props.orderId!;
      const params: NewOrderItemDTO[] = [];
      store.dataBrowse.fileds.forEach((item) => {
        item.data.forEach((f) => {
          params.push({ fileInforId: f, quantity: 1 });
        });
      });
      await newOrUpdateOrderItem_1(params, { orderId });
      await submitOrder(orderId);
      ElMessage({
        type: 'success',
        message: '提交订单申请成功，请先联系管理员审核',
      });
      // router.back();
    } catch (error) {
      console.log('🚀 ~ onSave ~ error:', error);
    } finally {
      saveLoading.value = false;
    }
  };

  const onQuit = () => {
    router.back();
  };

  onBeforeUnmount(() => {
    store.setData({ id: '', fileds: [] });
  });
</script>
