<template>
  <div class="flex h-full flex-col">
    <div class="h-0 flex-1">
      <el-scrollbar v-loading="loading" height="100%">
        <div class="px-5 pt-5">
          <div class="bg-w rounded p-4">
            <div v-if="!readonly" class="mb-4 flex">
              <el-button type="primary" @click="onAdd"> 添加成员 </el-button>
              <el-button type="danger" :disabled="!selectedMembers.length" @click="onBatchDelete"> 批量删除 </el-button>
            </div>

            <el-table
              ref="multipleTableRef"
              :data="tableData"
              style="width: 100%"
              class="c-table-header"
              @selection-change="handleSelectionChange"
            >
              <el-table-column v-if="!readonly" type="selection" width="55" />
              <el-table-column type="index" width="55" />
              <el-table-column prop="userName" label="用户名" min-width="120px" />
              <el-table-column prop="name" label="姓名" min-width="100px" />
              <el-table-column label="角色" min-width="150px">
                <template #default="{ row }">
                  <el-tag v-for="role in row.roles" :key="role.id" class="mr-1" size="small">
                    {{ role.roleName }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column v-if="!readonly" label="操作" width="150">
                <template #default="{ row }">
                  <el-button link type="primary" @click="onEdit(row)">分配角色</el-button>
                  <el-popconfirm title="确定删除此成员？" @confirm="onDelete(row)">
                    <template #reference>
                      <el-button link type="danger">删除</el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>

  <!-- 添加/编辑成员弹窗 -->
  <ProjectMemberDialog
    v-model="showDialog"
    :application-id="Number(id)"
    :member-data="currentMember"
    @success="onSuccess"
  />
</template>

<script setup lang="ts">
  import ProjectMemberDialog from './ProjectMemberDialog.vue';
  import { findApplicationUserByApplicationId, deleteApplicationUser } from '@/api/modules/xmzdcyglglmk';
  import { ElMessage, ElMessageBox } from 'element-plus';

  const props = defineProps({
    id: String,
    readonly: { type: Boolean, default: false },
  });

  const loading = ref(false);
  const tableData = ref<ApplicationUserVO[]>([]);
  const selectedMembers = ref<ApplicationUserVO[]>([]);
  const showDialog = ref(false);
  const currentMember = ref<ApplicationUserVO | null>(null);

  // 获取项目成员列表
  async function fetchData() {
    if (!props.id) return;

    try {
      loading.value = true;
      const { data } = await findApplicationUserByApplicationId({
        applicationId: Number(props.id),
      });
      tableData.value = data || [];
    } catch (error) {
      console.error('获取项目成员失败:', error);
      ElMessage.error('获取项目成员失败');
    } finally {
      loading.value = false;
    }
  }

  // 表格选择变化
  const handleSelectionChange = (val: ApplicationUserVO[]) => {
    selectedMembers.value = val;
  };

  // 添加成员
  const onAdd = () => {
    currentMember.value = null;
    showDialog.value = true;
  };

  // 编辑成员
  const onEdit = (member: ApplicationUserVO) => {
    currentMember.value = member;
    showDialog.value = true;
  };

  // 删除单个成员
  const onDelete = async (member: ApplicationUserVO) => {
    if (!member.userId) return;

    try {
      loading.value = true;
      await deleteApplicationUser({
        applicationId: Number(props.id),
        userIds: [member.userId],
      });
      ElMessage.success('删除成功');
      fetchData();
    } catch (error) {
      console.error('删除成员失败:', error);
      ElMessage.error('删除成员失败');
    } finally {
      loading.value = false;
    }
  };

  // 批量删除成员
  const onBatchDelete = async () => {
    if (!selectedMembers.value.length) return;

    try {
      await ElMessageBox.confirm('确定删除选中的成员吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      loading.value = true;
      const userIds = selectedMembers.value.map((member) => member.userId).filter((id) => id !== undefined) as number[];

      await deleteApplicationUser({
        applicationId: Number(props.id),
        userIds,
      });

      ElMessage.success('批量删除成功');
      fetchData();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量删除失败:', error);
        ElMessage.error('批量删除失败');
      }
    } finally {
      loading.value = false;
    }
  };

  // 操作成功回调
  const onSuccess = () => {
    fetchData();
  };

  // 组件挂载时获取数据
  onBeforeMount(() => {
    fetchData();
  });

  // 监听id变化，重新获取数据
  watch(
    () => props.id,
    () => {
      fetchData();
    }
  );
</script>

<style lang="scss" scoped>
  :deep(.el-table) {
    .el-table__row {
      cursor: pointer;
    }
  }
</style>
