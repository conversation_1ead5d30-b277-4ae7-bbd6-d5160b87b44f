<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑成员' : '添加成员'"
    width="80%"
    destroy-on-close
    @close="onClose"
  >
    <el-form ref="formRef" :model="form" label-width="100px">
      <!-- 用户选择 -->
      <el-form-item label="选择用户" prop="selectedUsers" v-if="!isEdit">
        <div class="w-full">
          <div class="mb-3 flex gap-4">
            <el-input
              v-model="userNameFilter"
              placeholder="请输入用户名查找"
              style="width: 200px"
              clearable
              @clear="onUserSearch"
              @keyup.enter="onUserSearch"
            />
            <el-input
              v-model="nameFilter"
              placeholder="请输入姓名查找"
              style="width: 200px"
              clearable
              @clear="onUserSearch"
              @keyup.enter="onUserSearch"
            />
            <el-button type="default" @click="onUserSearch">查询</el-button>
          </div>

          <el-table
            v-loading="userLoading"
            :data="userList"
            style="width: 100%"
            max-height="300px"
            class="c-table-header"
            row-key="id"
            @selection-change="handleUserSelectionChange"
          >
            <el-table-column type="selection" width="55" reserve-selection />
            <el-table-column prop="userName" label="用户名" />
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="email" label="邮箱" />
          </el-table>

          <!-- 分页 -->
          <div class="mt-3 flex justify-center">
            <el-pagination
              v-model:current-page="userPagination.page"
              :page-size="userPagination.pageSize"
              :total="userTotal"
              layout="prev, pager, next"
              @current-change="handleUserPageChange"
            />
          </div>
        </div>
      </el-form-item>

      <!-- 编辑模式显示用户信息 -->
      <template v-if="isEdit">
        <el-form-item label="用户名">
          <el-input :value="memberData?.userName" disabled />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input :value="memberData?.name" disabled />
        </el-form-item>
      </template>

      <!-- 角色选择 -->
      <el-form-item label="分配角色" prop="roleId">
        <el-select v-model="form.roleId" placeholder="请选择角色" filterable style="width: 200px">
          <el-option v-for="role in filteredRoleList" :key="role.id" :label="role.roleName" :value="role.id!" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="onSubmit">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { addApplicationUser, removeApplicationUserRole } from '@/api/modules/xmzdcyglglmk';
  import { findUserVOByCriteria } from '@/api/modules/sjzyxtyhglmk';
  import { findRolesByType } from '@/api/modules/jsglmk';
  import { ElMessage, ElMessageBox } from 'element-plus';

  interface Props {
    applicationId: number;
    memberData?: ApplicationUserVO | null;
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['success']);

  const dialogVisible = defineModel<boolean>({ required: true });

  const isEdit = computed(() => !!props.memberData);

  // 表单数据
  const form = reactive({
    selectedUsers: [] as number[],
    roleId: null as number | null,
  });

  // 用户搜索相关
  const userLoading = ref(false);
  const userNameFilter = ref('');
  const nameFilter = ref('');
  const userList = ref<MDMUserVO[]>([]);
  const userTotal = ref(0);
  const selectedUserIds = ref<number[]>([]);

  // 用户分页
  const userPagination = reactive({
    page: 1,
    pageSize: 10,
  });

  // 角色列表
  const roleList = ref<RoleVO[]>([]);

  // 过滤后的角色列表，只显示项目成员和项目负责人
  const filteredRoleList = computed(() => {
    return roleList.value.filter((role) => {
      const roleName = role.roleName?.toLowerCase() || '';
      return (
        roleName.includes('项目成员') ||
        roleName.includes('项目负责人') ||
        roleName.includes('成员') ||
        roleName.includes('负责人') ||
        roleName === '项目成员' ||
        roleName === '项目负责人'
      );
    });
  });

  // 提交loading
  const submitLoading = ref(false);
  const formRef = ref();

  // 获取用户列表
  async function fetchUserList() {
    try {
      userLoading.value = true;
      const { data } = await findUserVOByCriteria({
        userName: userNameFilter.value,
        name: nameFilter.value,
        pageNum: userPagination.page,
        pageSize: userPagination.pageSize,
      });
      userTotal.value = data?.totalElement || 0;
      userList.value = data?.content || [];
    } catch (error) {
      console.error('获取用户列表失败:', error);
    } finally {
      userLoading.value = false;
    }
  }

  // 获取角色列表
  async function fetchRoleList() {
    try {
      const { data } = await findRolesByType({ searchInput: '' });
      roleList.value = data || [];
    } catch (error) {
      console.error('获取角色列表失败:', error);
    }
  }

  // 用户查询
  const onUserSearch = () => {
    userPagination.page = 1;
    fetchUserList();
  };

  // 用户分页变化
  const handleUserPageChange = (page: number) => {
    userPagination.page = page;
    fetchUserList();
  };

  // 用户表格选择变化
  const handleUserSelectionChange = (val: MDMUserVO[]) => {
    selectedUserIds.value = val.map((item) => item.id!);
    form.selectedUsers = selectedUserIds.value;
  };

  // 提交表单
  const onSubmit = async () => {
    try {
      // 检查是否选择了用户（添加模式）
      if (!isEdit.value && form.selectedUsers.length === 0) {
        ElMessageBox.alert('请选择用户', '提示', {
          type: 'warning',
        });
        return;
      }

      // 检查是否选择了角色
      if (!form.roleId) {
        ElMessageBox.alert('请选择角色', '提示', {
          type: 'warning',
        });
        return;
      }

      submitLoading.value = true;

      if (isEdit.value) {
        // 编辑模式：更新角色
        if (props.memberData?.userId && props.memberData?.roles) {
          // 先移除现有角色
          const currentRoleIds = props.memberData.roles.map((role) => role.id!);
          if (currentRoleIds.length > 0) {
            await removeApplicationUserRole({
              applicationId: props.applicationId,
              userId: props.memberData.userId,
              roleIds: currentRoleIds,
            });
          }
        }

        // 重新添加角色
        const userRolePairs: UserRolePair[] = [];
        if (form.roleId) {
          userRolePairs.push({
            userId: props.memberData!.userId!,
            roleId: form.roleId,
          });
        }

        await addApplicationUser({
          applicationId: props.applicationId,
          userRolePairs,
        });
      } else {
        // 添加模式
        const userRolePairs: UserRolePair[] = [];
        if (form.roleId) {
          form.selectedUsers.forEach((userId) => {
            userRolePairs.push({ userId, roleId: form.roleId! });
          });
        }

        await addApplicationUser({
          applicationId: props.applicationId,
          userRolePairs,
        });
      }

      ElMessage.success(isEdit.value ? '更新成功' : '添加成功');
      emit('success');
      onClose();
    } catch (error) {
      console.error('操作失败:', error);
    } finally {
      submitLoading.value = false;
    }
  };

  // 关闭弹窗
  const onClose = () => {
    dialogVisible.value = false;
    // 重置表单
    Object.assign(form, {
      selectedUsers: [],
      roleId: null,
    });
    selectedUserIds.value = [];
    userNameFilter.value = '';
    nameFilter.value = '';
    userPagination.page = 1;
    formRef.value?.clearValidate();
  };

  // 监听弹窗显示状态
  watch(dialogVisible, (visible) => {
    if (visible) {
      fetchRoleList();
      if (!isEdit.value) {
        fetchUserList();
      } else {
        // 编辑模式，预填充角色数据
        if (props.memberData?.roles && props.memberData.roles.length > 0) {
          form.roleId = props.memberData.roles[0].id!;
        }
      }
    }
  });
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    max-height: 70vh;
    overflow-y: auto;
  }
</style>
